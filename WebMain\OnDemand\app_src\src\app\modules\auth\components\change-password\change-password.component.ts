import {
  AfterViewInit,
  Component,
  Inject,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  Optional
} from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { DomSanitizer } from '@angular/platform-browser'
import { ActivatedRoute, Router } from '@angular/router'
import { MicroAppAuthLoaderComponent } from '@auth/components/micro-app-auth-loader/micro-app-auth-loader.component'
import { MicroAppAuthService } from '@auth/services/micro-app-auth.service'
import { MicroAppAuthService } from '@auth/services/micro-app-auth.service'
import { ConfigService } from '@config/services/config.service'
import { select, Store } from '@ngrx/store'
import {
  IframeMessengerService,
  MessageContent
} from '@root/modules/micro-apps/config/iframe-messenger.service'
import { AppIdentitiesTypes } from '@root/modules/micro-apps/models/app-identities-types'
import { NavigateToAction } from '@shared/store/actions'
import { BsModalRef } from 'ngx-bootstrap/modal'
import { ToastrService } from 'ngx-toastr'
import { Subject } from 'rxjs'
import { debounceTime, filter, map, takeUntil } from 'rxjs/operators'
import { ResetPasswordModel } from 'src/app/modules/auth/models/reset-password.model'
import { ConfigState } from 'src/app/modules/config/store/reducers'
import {
  getControlSetting,
  getThemeClient
} from 'src/app/modules/config/store/selectors'
import { NotificationService } from 'src/app/services/notification.service'
import { ChangePassword, EmailUpdate } from '../../store/actions'
import { AuthState } from '../../store/reducers'
import {
  changePasswordSuccess,
  passwordComplexity
} from '../../store/selectors/access.selectors'

declare let encryptStr: any

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss'],
  providers: [MicroAppAuthService]
})
export class ChangePasswordComponent
  extends MicroAppAuthLoaderComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @Input() isForcedReset: boolean

  @Input() token: string

  @Input() modalReference: BsModalRef

  @Input()
  isFBIEnabled: boolean

  changePasswordForm: FormGroup

  changePasswordSubmitted = false

  client: string

  unsubscribed$ = new Subject<void>()

  /**
   * Application password complexity options from API
   */
  complexityOptions: string[]

  get form() {
    return this.changePasswordForm.controls
  }

  passWordExpired: boolean

  disableButton = false

  constructor(
    public microAppAuthService: MicroAppAuthService,
    private configStore: Store<ConfigState>,
    private router: Router,
    public store: Store<AuthState>,
    private toastr: NotificationService,
    private fb: FormBuilder,
    private _route: ActivatedRoute,
    public configService: ConfigService,
    @Optional() public dialogRef: MatDialogRef<ChangePasswordComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data,
    public activatedRoute: ActivatedRoute,
    public notify: ToastrService,
    public iframeMessengerService: IframeMessengerService,
    public sanitizer: DomSanitizer
  ) {
    super(
      microAppAuthService,
      store,
      iframeMessengerService,
      sanitizer,
      activatedRoute
    )

    const isFromNavbarLink =
      typeof data == 'object' && data !== null ? data?.fromNavbarLink : false

    if (typeof data === 'object' && data?.isFBIEnabled !== undefined) {
      this.isFBIEnabled = data?.isFBIEnabled
    }

    this.microAppAuthService.setPath = `#/reset-password?isFromNavbarLink=${isFromNavbarLink}`

    this.changePasswordForm = this.fb.group(
      {
        oldPassword: ['', Validators.required],
        newPassword: ['', Validators.required],
        confirmPassword: [null, Validators.required]
      },
      {
        validator: this.checkIfMatchingPasswords(
          'newPassword',
          'confirmPassword'
        )
      }
    )

    this.passWordExpired =
      typeof data === 'boolean' ? data : data?.notifyForPasswordChange
  }

  ngOnInit() {
    this.store
      .select(getControlSetting('ENABLE_REVIEW_2'))
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((isFbiEnabled) => {
        this.isFBIEnabled = Boolean(isFbiEnabled)
      })

    this.#selectLoginResponseFromChildApp()
    this.configStore
      .pipe(select(getThemeClient), takeUntil(this.unsubscribed$))
      .subscribe((client: string) => {
        this.client = client
      })
    this._route.queryParamMap.subscribe((queryParams) => {
      const generatePw = queryParams?.get('generatePw')?.toLowerCase()
      const email = queryParams?.get('email')
      const id = queryParams?.get('userId')
      if (queryParams.has('token')) {
        this.token = queryParams.get('token')
      }
      if (email) {
        this.store.dispatch(new EmailUpdate(+id, email))
      }
      if (generatePw === 'false') {
        this.store.dispatch(new NavigateToAction('/login', true))
      }
    })

    // this.store.dispatch(new GetUserInformation())
    // this.store
    //   .pipe(
    //     select(userInformation),
    //     takeUntil(this.unsubscribed$),
    //     map((res: UserInformation) => res),
    //     filter((res) => !!res)
    //   )
    //   .subscribe((res: UserInformation) => {
    //     this.disableButton = res?.isADUser
    //   })
  }

  public ngAfterViewInit(): void {
    super.ngAfterViewInit()
  }

  public ngOnDestroy(): void {
    this.microAppAuthService.clearPath()
  }

  onChangePassword() {
    this.changePasswordSubmitted = true
    const key = '92fdce43453434c36f8e3fbcb2b2e4fb'
    const passwordModel = new ResetPasswordModel()
    const newP = this.changePasswordForm.get('newPassword').value || ''
    const oldP = this.changePasswordForm.get('oldPassword').value || ''
    passwordModel.newPassword = encryptStr(newP, key)
    passwordModel.oldPassword = encryptStr(oldP, key)
    passwordModel.key = key
    if (this.isForcedReset) {
      this.changePasswordForm.get('oldPassword').clearValidators()
      this.changePasswordForm.get('oldPassword').updateValueAndValidity()
    }
    if (this.changePasswordForm.invalid) {
      return
    }
    this.changePasswordForm.reset()
    this.changePasswordSubmitted = false
    if (!this.isForcedReset) {
      if (newP === oldP) {
        this.toastr.showError(
          'New password cannot be the same as the current password.',
          true
        )
        return
      }
    }
    this.store.dispatch(new ChangePassword(passwordModel, this.token))

    this.initPasswordComplexityOptions()

    this.passwordChangeSuccessful()
  }

  /**
   * Populates the password complexity criteria from the response object.
   * Options are returned as array of string.
   */
  private initPasswordComplexityOptions() {
    this.complexityOptions = []
    this.store
      .pipe(
        select(passwordComplexity),
        debounceTime(200),
        filter((_) => _ && _.length > 0),
        takeUntil(this.unsubscribed$)
      )
      .subscribe({ next: (options) => (this.complexityOptions = options) })
  }

  checkIfMatchingPasswords(
    passwordKey: string,
    passwordConfirmationKey: string
  ) {
    return (group: FormGroup) => {
      const passwordInput = group.controls[passwordKey],
        passwordConfirmationInput = group.controls[passwordConfirmationKey]
      if (passwordInput.value !== passwordConfirmationInput.value) {
        return passwordConfirmationInput.setErrors({ notEquivalent: true })
      } else {
        return passwordConfirmationInput.setErrors(null)
      }
    }
  }

  private passwordChangeSuccessful() {
    this.store
      .pipe(
        select(changePasswordSuccess),
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((res) => {
        if (res['data'].isPasswordResetSuccessful) {
          this.dialogRef.close()
        }
      })
  }

  #selectLoginResponseFromChildApp(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (m) =>
            m.eventTriggeredBy === AppIdentitiesTypes.VENIO_NEXT &&
            m.eventTriggeredFor === 'PARENT_WINDOW' &&
            (m.payload as MessageContent).content['isNewPasswordChangeSuccess']
        ),
        map((mc) => mc.payload as MessageContent),
        debounceTime(100),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((mc) => {
        const { navigateTo } = mc.content as any
        const hasToken = this._route.snapshot.queryParamMap.has('token')
        // if it isn't from routed page but rather from dialog after login success,
        // we have used the 'isParentAppLinkRequest' to identify the request
        // in the child app where a notified flag is used to close the dialog
        if (!hasToken && mc.content['isParentAppLinkRequest']) {
          this.toastr.showSuccess('Password changed successfully', true)
          this.dialogRef.close(true)
          return
        }
        // Probably to /login page or if any custom redirection after password change success
        this.store.dispatch(new NavigateToAction(navigateTo || '/login', true))
      })
  }
}
