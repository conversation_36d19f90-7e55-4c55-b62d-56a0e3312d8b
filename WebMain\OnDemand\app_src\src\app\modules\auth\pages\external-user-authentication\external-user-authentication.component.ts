import { DOCUMENT } from '@angular/common'
import { Component, Inject, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core'
import { NgForm } from '@angular/forms'
import { DomSanitizer } from '@angular/platform-browser'
import { ActivatedRoute, Router } from '@angular/router'
import { MicroAppAuthLoaderComponent } from '@auth/components/micro-app-auth-loader/micro-app-auth-loader.component'
import { MicroAppAuthService } from '@auth/services/micro-app-auth.service'
import { ConfigState } from '@config/store/reducers'
import { getControlSetting } from '@config/store/selectors'
import { select, Store } from '@ngrx/store'
import { Store as XsStore } from '@ngxs/store'
import {
  IframeMessengerService,
  MessageContent,
  MessageType
} from '@root/modules/micro-apps/config/iframe-messenger.service'
import { AppIdentitiesTypes } from '@root/modules/micro-apps/models/app-identities-types'
import { InitUploadHistoryQueryAction } from '@stores/actions/upload.actions'
import { ToastrService } from 'ngx-toastr'
import { Observable, Subject } from 'rxjs'
import { debounceTime, filter, map, takeUntil } from 'rxjs/operators'
import { ConfigService } from 'src/app/modules/config/services/config.service'
import { GlobalLogOutAction } from 'src/app/store/actions'
import {
  ExtUserAuthResponseModel,
  HoldUserAuthResponseModel
} from '../../models/external-user-authentication.model'
import { UserModel } from '../../models/user.model'
import { AuthService } from '../../services/auth.service'
import {
  ClearStoreProperty,
  SendExtUserAuthNotification,
  SendHoldUserAuthNotification,
  SetServerSideSession,
  VerifyExtUserAuthCode,
  VerifyHoldUserAuthCode
} from '../../store/actions'
import { AuthState } from '../../store/reducers'
import {
  extUserNotificationSentResponse,
  getAccessToken,
  getExtUserAuthErrorMessage,
  getExtUserAuthModel,
  getHoldCUstodianAuthModel,
  getUser,
  holdCustNotificationSentResponse,
  isExtUserAuthSuccessful
} from '../../store/selectors/access.selectors'
import { SearchQueryModule } from './../../../shared/models/documents.model'

@Component({
  selector: 'app-external-user-authentication',
  templateUrl: './external-user-authentication.component.html',
  styleUrls: ['./external-user-authentication.component.scss'],
  providers: [MicroAppAuthService]
})
export class ExtUserAuthComponent
  extends MicroAppAuthLoaderComponent
  implements OnInit, OnDestroy
{
  @ViewChild('extUserForm')
  extUserForm: NgForm

  verificationCode: ''

  showError = false

  isSecurityCodeSent: boolean

  isExtUserAuthSuccessful$: Observable<boolean>

  verificationResponseMessage: string

  ResendSecurityCodeMessage: string

  extUserAuthModel: ExtUserAuthResponseModel

  extUserId: number

  clearCachePath: string

  projectId: number

  logType: string

  tokenType: string

  linkToken: string

  /**
   * logo full path
   */
  logoFullPath: string

  private unsubscribed$ = new Subject<void>()

  /** true when the control setting to enable second review set view */
  enableReview2: boolean

  holdId: number

  custId: number

  holdToken: string

  custodianAuthModel: HoldUserAuthResponseModel

  custodianNoticeId: number

  //import specific variables
  importId: number

  isOverlay: boolean

  routePath: string

  constructor(
    private authenticationService: AuthService,
    private configStore: Store<ConfigState>,
    private route: ActivatedRoute,
    private router: Router,
    private xsStore: XsStore,
    @Inject(DOCUMENT)
    private _document: HTMLDocument,
    public activatedRoute: ActivatedRoute,
    public store: Store<AuthState>,
    public microAppAuthService: MicroAppAuthService,
    public notify: ToastrService,
    public configService: ConfigService,
    public iframeMessengerService: IframeMessengerService,
    public sanitizer: DomSanitizer
  ) {
    super(
      microAppAuthService,
      store,
      iframeMessengerService,
      sanitizer,
      activatedRoute
    )

    this.microAppAuthService.setPath = '#/external-user-login'

    this.clearCachePath = localStorage.getItem('ClearCachePath')

    localStorage.clear()
    this.store.dispatch(new GlobalLogOutAction())

    this.route.queryParams.pipe(takeUntil(this.unsubscribed$)).subscribe({
      next: (params) => {
        this.projectId = +params['projectId']
        this.tokenType = params['docShareToken']
          ? 'sharedoc'
          : params['userWiseToken'] && params['invitation'] === 'production'
          ? 'production'
          : params['holdToken']
          ? 'legalhold'
          : params['repairToken']
          ? 'repair'
          : params['importRepairToken']
          ? 'import_repair'
          : params['tokenType'] === 'import'
          ? 'import'
          : params['tokenType'] === 'import_download_log'
          ? 'import_download_log'
          : 'upload'
        this.linkToken =
          this.tokenType === 'sharedoc'
            ? params['docShareToken']
            : this.tokenType === 'repair'
            ? params['repairToken']
            : this.tokenType === 'import' ||
              this.tokenType === 'import_download_log'
            ? params['latestUploadInviteToken']
            : this.tokenType === 'import_repair'
            ? params['importRepairToken']
            : params['userWiseToken']

        this.extUserId = params['extUserId']
        if (this.extUserId) {
          this.store.dispatch(
            new SendExtUserAuthNotification(this.extUserId, false)
          )
        }
        this.holdId = params['holdId']
        this.custId = params['custodianId']
        this.holdToken = params['holdToken']
        this.custodianNoticeId = params['custodianNoticeId']
        if (this.custId) {
          this.store.dispatch(
            new SendHoldUserAuthNotification(this.custId, false)
          )
        }

        if (this.tokenType === 'import') {
          this.importId = params['importId']
          this.isOverlay = params['isOverlay']
          this.routePath = params['routePath']
        } else if (this.tokenType === 'import_download_log') {
          this.importId = params['importId']
          this.projectId = params['projectId']
          this.logType = params['logType']
        } else if (this.tokenType === 'import_repair') {
          this.importId = params['importId']
          this.projectId = params['projectId']
        }
      }
    })

    // set favorite icon full path
    let faviconFullPath =
      configService.getWebBaseUrl() + '/' + configService.favIconPath
    if (
      this.configService.favIconPath.startsWith('http://') ||
      this.configService.favIconPath.startsWith('https://')
    ) {
      faviconFullPath = this.configService.favIconPath
    }
    this._document
      .getElementById('appFavicon')
      .setAttribute('href', faviconFullPath)

    // set logo full path data
    if (
      this.configService.logoPath.startsWith('http://') ||
      this.configService.logoPath.startsWith('https://')
    ) {
      this.logoFullPath = this.configService.logoPath
    } else {
      this.logoFullPath =
        this.configService.getWebBaseUrl() + '/' + this.configService.logoPath
    }
  }

  ngOnInit() {
    this.configStore
      .pipe(
        select(getControlSetting('ENABLE_REVIEW_2')),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((isEnabled: boolean) => {
        this.enableReview2 = isEnabled
      })

    this.store
      .pipe(
        select(getAccessToken),
        takeUntil(this.unsubscribed$),
        map((token: string) => token),
        filter((token: string) => !!token)
      )
      .subscribe((token: string) => {
        this.authenticationService.setAccessToken(token)
        this.clearCachePath = localStorage.getItem('ClearCachePath')
      })

    this.store
      .pipe(
        select(getExtUserAuthErrorMessage),
        takeUntil(this.unsubscribed$),
        map((err: string) => err),
        filter((error: string) => !!error)
      )
      .subscribe((err: string) => {
        this.showError = true
        this.verificationResponseMessage = err

        if (err) {
          this.#notifyChildAppForStateChange({
            errorMessage: this.verificationResponseMessage,
            isSecurityCodeLoginProgress: false,
            successMessage: ''
          })
        }

        if (this.isSecurityCodeSent === true) {
          this.isSecurityCodeSent = false
        }
      })

    this.store
      .pipe(
        select(extUserNotificationSentResponse),
        takeUntil(this.unsubscribed$),
        map((res: ExtUserAuthResponseModel) => res)
      )
      .subscribe((codeSentResponse: ExtUserAuthResponseModel) => {
        if (codeSentResponse) {
          if (codeSentResponse.message) {
            this.ResendSecurityCodeMessage = codeSentResponse.message
            this.isSecurityCodeSent = true
            if (this.showError === true) {
              this.showError = false
            }

            this.#notifyChildAppForStateChange({
              successMessage: this.ResendSecurityCodeMessage,
              isSecurityCodeLoginProgress: false,
              errorMessage: ''
            })
          } else {
            if (this.showError === true) {
              this.showError = false
            }
          }

          setTimeout(
            function () {
              this.isSecurityCodeSent = false
              this.store.dispatch(
                new ClearStoreProperty('extUserNotificationSentResponse')
              )
            }.bind(this),
            5000
          )
        }
      })

    this.store
      .pipe(
        select(holdCustNotificationSentResponse),
        takeUntil(this.unsubscribed$),
        map((res: HoldUserAuthResponseModel) => res)
      )
      .subscribe((codeSentResponse: HoldUserAuthResponseModel) => {
        if (codeSentResponse) {
          if (codeSentResponse.message) {
            this.ResendSecurityCodeMessage = codeSentResponse.message
            this.isSecurityCodeSent = true
            if (this.showError === true) {
              this.showError = false
            }
          } else {
            if (this.showError === true) {
              this.showError = false
            }
          }

          setTimeout(
            function () {
              this.isSecurityCodeSent = false
              this.store.dispatch(
                new ClearStoreProperty('holdCustNotificationSentResponse')
              )
            }.bind(this),
            5000
          )
        }
      })

    // response from onVerify call
    this.vefiryResponse()

    this.isExtUserAuthSuccessful$ = this.store.pipe(
      select(isExtUserAuthSuccessful),
      takeUntil(this.unsubscribed$)
    )

    this.#selectFormDataFromChildApp()
  }

  ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
    this.store.dispatch(new ClearStoreProperty('error'))
    this.store.dispatch(
      new ClearStoreProperty('extUserNotificationSentResponse')
    )
    this.store.dispatch(new ClearStoreProperty('isExtUserAuthSuccessful'))
  }

  /*
   * 1. verify code -> 2. Create AuthToken -> 3. User Detail (login) -> 4. User -> getUser
   */
  onVerify(): void {
    this.verificationCode = this.extUserForm.controls['verificationCode'].value
    if (this.custId) {
      this.store.dispatch(
        new VerifyHoldUserAuthCode(
          this.custId,
          this.verificationCode,
          this.holdToken,
          this.tokenType
        )
      )
    } else {
      this.store.dispatch(
        new VerifyExtUserAuthCode(
          this.extUserId,
          this.verificationCode,
          this.linkToken,
          this.tokenType,
          this.projectId
        )
      )
    }
  }

  /*
   * response of verify call in ngOnInit
   */
  vefiryResponse(): void {
    this.store
      .pipe(
        select(getExtUserAuthModel),
        takeUntil(this.unsubscribed$),
        filter((model: ExtUserAuthResponseModel) => !!model),
        map((model: ExtUserAuthResponseModel) => model)
      )
      .subscribe((model: ExtUserAuthResponseModel) => {
        this.extUserAuthModel = model

        if (!this.extUserAuthModel.token) {
          this.showError = true
          this.verificationResponseMessage = this.extUserAuthModel.message
          this.#notifyChildAppForStateChange({
            errorMessage: this.verificationResponseMessage,
            isSecurityCodeLoginProgress: false,
            successMessage: ''
          })
        }
      })

    this.store
      .pipe(
        select(getHoldCUstodianAuthModel),
        takeUntil(this.unsubscribed$),
        filter((model: HoldUserAuthResponseModel) => !!model),
        map((model: HoldUserAuthResponseModel) => model)
      )
      .subscribe((model: HoldUserAuthResponseModel) => {
        this.custodianAuthModel = model
      })

    this.store
      .pipe(
        select(getUser),
        takeUntil(this.unsubscribed$),
        map((user: UserModel) => user),
        filter((user: UserModel) => !!user)
      )
      .subscribe((user: UserModel) => {
        if (user && this.tokenType === 'legalhold') {
          this.router.navigate([`/launchpad/custodian-portal/${this.custId}`], {
            queryParams: {
              custodianNoticeId: this.custodianNoticeId
            }
          })
        } else {
          this.authenticationService.setClientSideSession(user.Script)
          this.store.dispatch(new SetServerSideSession(user, false, null, true))
          // show share document
          if (user && this.extUserAuthModel && this.tokenType === 'sharedoc') {
            // this.xsStore.dispatch(
            //   new InitUploadHistoryQueryAction({
            //     query: `FOLDERS("${this.extUserAuthModel.folderLineage}")`,
            //     includePc: false
            //   })
            // )
            this.xsStore.dispatch(
              new InitUploadHistoryQueryAction({
                query: `FOLDERS("${this.extUserAuthModel.folderLineage}")`,
                includePc: false,
                documentShareToken: this.linkToken,
                sourceModule: SearchQueryModule.DocumentShare
              })
            )

            this.router.navigate(
              [`${this.enableReview2 ? '/review2' : '/review'}`],
              {
                queryParams: {
                  projectId: this.projectId,
                  extUserId: this.extUserId,
                  docShareToken: this.linkToken ///documentShareToken
                }
              }
            )
          } else if (user && this.tokenType === 'upload') {
            this.router.navigate(['/upload'], {
              queryParams: {
                projectId: this.projectId,
                extUserId: this.extUserId
              }
            })
          } else if (user && this.tokenType === 'repair') {
            this.router.navigate(['/repair'], {
              queryParams: {
                projectId: this.projectId,
                extUserId: this.extUserId,
                settingId: -1,
                token: this.linkToken
              }
            })
          } else if (user && this.tokenType === 'import_repair') {
            this.router.navigate(['/repair'], {
              queryParams: {
                projectId: this.projectId,
                extUserId: this.extUserId,
                importId: this.importId,
                token: this.linkToken
              }
            })
          } else if (user && this.tokenType === 'import') {
            this.router.navigate(['/import/' + this.routePath], {
              queryParams: {
                projectId: this.projectId,
                extUserId: this.extUserId,
                importId: this.importId,
                isOverlay: this.isOverlay
              }
            })
          } else if (user && this.tokenType === 'import_download_log') {
            this.router.navigate(['/download'], {
              queryParams: {
                projectId: this.projectId,
                importId: this.importId,
                logType: this.logType,
                extUserId: this.extUserId
              }
            })
          } else if (user) {
            this.router.navigate(['/production'], {
              queryParams: {
                projectId: this.projectId,
                extUserId: this.extUserId
              }
            })
          }
        }
      })
  }

  onCloseVerificationFailed(): void {
    this.showError = false
    this.store.dispatch(new ClearStoreProperty('error'))
    this.store.dispatch(new ClearStoreProperty('isExtUserAuthSuccessful'))
  }

  resendNotification(): void {
    this.store.dispatch(
      new ClearStoreProperty('extUserNotificationSentResponse')
    )
    if (this.extUserId)
      this.store.dispatch(new SendExtUserAuthNotification(this.extUserId, true))
    else if (this.custId)
      this.store.dispatch(new SendHoldUserAuthNotification(this.custId, true))
  }

  onCloseResendSecurityCodeBox(): void {
    this.isSecurityCodeSent = false
    this.store.dispatch(
      new ClearStoreProperty('extUserNotificationSentResponse')
    )
  }

  #notifyChildAppForStateChange(payload: {
    errorMessage: string
    successMessage: string
    isSecurityCodeLoginProgress: boolean
  }): void {
    this.iframeMessengerService.sendMessage({
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'FRAME_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.AUTH_UPDATE,
        content: {
          ...payload,
          securityCodeChildMessage: true
        }
      }
    })
  }

  #selectFormDataFromChildApp(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (m) =>
            m.eventTriggeredBy === AppIdentitiesTypes.VENIO_NEXT &&
            m.eventTriggeredFor === 'PARENT_WINDOW' &&
            (m.payload as MessageContent).type === MessageType.AUTH_UPDATE &&
            (m.payload as MessageContent).content['isTwoFactorAuthCodeSystem']
        ),
        map((mc) => (mc.payload as MessageContent)?.content),
        debounceTime(100),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((data: any) => {
        this.onCloseVerificationFailed()
        const { isRequestNewCode, isLoinInvoked, securityCodeControlValue } =
          data

        if (isRequestNewCode && !isLoinInvoked) {
          this.resendNotification()
        }

        if (isLoinInvoked && !isRequestNewCode) {
          this.verificationCode = securityCodeControlValue

          this.extUserForm.form.patchValue({
            verificationCode: this.verificationCode
          })
          this.extUserForm.form.updateValueAndValidity()
          this.onVerify()
        }
      })
  }
}
