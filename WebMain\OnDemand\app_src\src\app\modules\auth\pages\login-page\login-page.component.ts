import { DOCUMENT } from '@angular/common'
import {
  Component,
  Inject,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
  ViewEncapsulation
} from '@angular/core'
import { NgForm } from '@angular/forms'
import { MatDialog } from '@angular/material/dialog'
import { Dom<PERSON>anitizer, Title } from '@angular/platform-browser'
import { ActivatedRoute, Router } from '@angular/router'
import { ChangePasswordComponent } from '@auth/components/change-password/change-password.component'
import { MicroAppAuthLoaderComponent } from '@auth/components/micro-app-auth-loader/micro-app-auth-loader.component'
import { MicroAppAuthService } from '@auth/services/micro-app-auth.service'
import { ConfigService } from '@config/services/config.service'
import {
  FetchBaseSettings,
  ResetActiveProjectInfo
} from '@config/store/actions'
import { getControlSetting } from '@config/store/selectors'
import { select, Store } from '@ngrx/store'
import { Store as xsStore } from '@ngxs/store'
import {
  IframeMessengerService,
  MessageContent,
  MessageType
} from '@root/modules/micro-apps/config/iframe-messenger.service'
import { AppIdentitiesTypes } from '@root/modules/micro-apps/models/app-identities-types'
import { MaintenanceInfoModel } from '@shared/models'
import { NavService } from '@shared/services/nav.service'
import {
  FetchProjectList,
  InitStartupAction,
  SetSelectedProjectIdAction
} from '@stores/actions'
import { AppStartupState } from '@stores/states/startups.state'
import { JsonConvert } from 'json2typescript'
import * as moment from 'moment'
import { CookieService } from 'ngx-cookie-service'
import { ToastrService } from 'ngx-toastr'
import { StateResetAll } from 'ngxs-reset-plugin'
import { combineLatest, Observable, of, Subject } from 'rxjs'
import {
  concatMap,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  take,
  takeUntil
} from 'rxjs/operators'
import { GetUserLayoutId } from 'src/app/modules/review/xs-store'
import { GlobalLogOutAction } from '../../../../store/actions'
import * as fromSharedCaseAction from '../../../shared/store/actions/case.actions'
import { Login } from '../../models/login.model'
import { User, UserModel } from '../../models/user.model'
import { AuthService } from '../../services/auth.service'
import {
  ClearAuthStore,
  ClearStoreProperty,
  FetchAuthToken,
  FetchAuthTokenSuccess,
  FetchMaintenanceSetting,
  FetchUser,
  FetchUserDetails,
  HideLoginSpinner,
  SendResetPasswordLink,
  SetIsAddOnBoxChecked,
  SetServerSideSession,
  ShowLoginSpinner
} from '../../store/actions'
import { AuthState } from '../../store/reducers'
import {
  getAccessToken,
  getErrorMessage,
  getRefreshToken,
  getUser,
  isAuthSuccessful,
  isResetPasswordLinkSent,
  maintenanceSettingInfo,
  showLoginSpinner
} from '../../store/selectors/access.selectors'

@Component({
  selector: 'app-login-page',
  templateUrl: './login-page.component.html',
  styleUrls: ['./login-page.component.scss'],
  encapsulation: ViewEncapsulation.None,
  host: { class: 'login-page' },
  providers: [MicroAppAuthService]
})
export class LoginPageComponent
  extends MicroAppAuthLoaderComponent
  implements OnInit, OnDestroy
{
  @ViewChild('loginForm') public loginForm: NgForm

  forgotPassword = false

  showError = false

  errorMessage = ''

  errorTitle = 'Login Failed!!'

  isResetSuccessful = false

  isResetFailed = false

  isBrowserVersionSupportedFlag: boolean

  browserSupportMsg: string

  notSupported = false

  isCookieEnabled: boolean

  clearCachePath: string

  showSpinner$: Observable<boolean>

  isAuthSuccessful$: Observable<boolean>

  venioVersion: string

  private _companyName: string

  isVodrEnabled = false

  login: Login = {
    userName: '',
    password: '',
    userNameForgotten: null
  }

  /**
   * login background image full path
   */
  loginBackgroundImagePath: string

  /**
   * logo full path
   */
  logoFullPath: string

  /**
   * check if system maintenance is enable or not and check the maintenance start date
   */
  isMaintenanceEnable = false

  /**
   * check if system maintenance is enable or not and check the maintenance prior start date
   */
  showAlertMsg = false

  /**
   * instance of maintenance model
   */
  maintenanceInfo: MaintenanceInfoModel

  isADEnabled: boolean //Check if AD is enabled

  isFBIEnabled: boolean

  private unsubscribed$ = new Subject<void>()

  /**
   * The specified version of the VOD version in control settings.
   */
  VOD_VERSION: number

  get vodVersion(): number {
    return this.VOD_VERSION
  }

  /**
   * Flag to indicate if the control settings are still loading.
   * This is used to prevent the login page from rendering randomly.
   */
  isControlSettingLoading = true

  constructor(
    private _router: Router,
    private authenticationService: AuthService,
    public configService: ConfigService,
    private navService: NavService,
    public store: Store<AuthState>,
    private cookie: CookieService,
    private titleService: Title,
    private xsStore: xsStore,
    @Inject(DOCUMENT)
    private _document: HTMLDocument,
    private dialog: MatDialog,
    public activatedRoute: ActivatedRoute,
    public notify: ToastrService,
    public iframeMessengerService: IframeMessengerService,
    public sanitizer: DomSanitizer,
    public microAppAuthService: MicroAppAuthService
  ) {
    super(
      microAppAuthService,
      store,
      iframeMessengerService,
      sanitizer,
      activatedRoute
    )
    this.microAppAuthService.clearPath()
    this.#selectLoginResponseFromChildApp()
    navService.setReturnUrl(null)

    // set favorite icon full path
    let faviconFullPath =
      configService.getWebBaseUrl() + '/' + configService.favIconPath
    if (
      this.configService.favIconPath.startsWith('http://') ||
      this.configService.favIconPath.startsWith('https://')
    ) {
      faviconFullPath = this.configService.favIconPath
    }
    this._document
      .getElementById('appFavicon')
      .setAttribute('href', faviconFullPath)

    // set logo full path data
    if (
      this.configService.logoPath.startsWith('http://') ||
      this.configService.logoPath.startsWith('https://')
    ) {
      this.logoFullPath = this.configService.logoPath
    } else {
      this.logoFullPath =
        this.configService.getWebBaseUrl() + '/' + this.configService.logoPath
    }

    // The soonest we can get the VOD version is when the control settings are fetched.
    // Resolving the VOD version is crucial for the login page.
    this.#selectVodVersion()
  }

  ngOnInit(): void {
    // set login background as full path
    this.setLoginBackground()
    // state which should not be reset has to be pass on StateResetAll's parameters.
    this.xsStore.dispatch(new StateResetAll())

    //localStorage.clear()
    this.store.dispatch(new ClearAuthStore())

    //selectedProjectId to be reset
    this.xsStore.dispatch(new SetSelectedProjectIdAction(null))

    // TODO This needs to be put into a common place where we can control all the clearing of state on logout
    // Clear active project info
    this.store.dispatch(new ResetActiveProjectInfo())
    // Clear project list from the shared module store
    this.store.dispatch(fromSharedCaseAction.unsetProjectList())

    this.isBrowserVersionSupported()
    this.areCookiesEnabled()
    this.isVodrEnabled = !this.configService.isVodEnabled
    this._companyName = this.configService.companyName
    this.venioVersion = this.configService.venioVersion
    this.isADEnabled = this.configService.isADEnabled
    if (this.isVodrEnabled) this.titleService.setTitle('Ricoh OnDemand - Login')
    else this.titleService.setTitle('VenioOne OnDemand - Login')

    this.activatedRoute.queryParamMap.subscribe((params) => {
      if (
        params.get('Session') === 'expire' ||
        params.get('session') === 'expire'
      ) {
        this.errorMessage = 'Session expired.'
        this.errorTitle = 'Session'
        this.showError = true
      }
    })

    this.store
      .pipe(
        select(getAccessToken),
        takeUntil(this.unsubscribed$),
        map((token: string) => token),
        filter((token: string) => !!token)
      )
      .subscribe((token: string) => {
        this.store.dispatch(new FetchUserDetails(this.login.password))
        this.authenticationService.setAccessToken(token)
      })
    this.store
      .pipe(
        select(getRefreshToken),
        takeUntil(this.unsubscribed$),
        map((token: string) => token),
        filter((token: string) => !!token)
      )
      .subscribe((token: string) => {
        this.authenticationService.setRefreshToken(token)
      })

    this.isAuthSuccessful$ = this.store.pipe(
      select(isAuthSuccessful),
      takeUntil(this.unsubscribed$)
    )

    this.store
      .pipe(
        select(getUser),
        takeUntil(this.unsubscribed$),
        map((user: UserModel) => user),
        filter((user: UserModel) => !!user)
      )
      .subscribe((user: UserModel) => {
        const isPasswordExpired = user.PasswordExpired
        if (user.Remark?.includes('Password is expiring')) {
          this.notify.warning(user.Remark)
        }
        if (isPasswordExpired) {
          // if the password is expired, clear the local storage and redirect to login page
          localStorage.clear()

          if (this.isFBIEnabled) {
            this.notify.error(user.Remark)
          }
          this.handlePwChange(isPasswordExpired, user.Remark)
        } else {
          // TODO This will be the only action to get user details in the future. The entire flow needs to change.
          this.store.dispatch(new FetchUser())
          AppStartupState.userDetail = new JsonConvert().deserializeObject(
            user,
            User
          )

          if (
            this.configService.isTwoFactorAuthenticationEnabled === true &&
            user.EmailAddress != ''
          ) {
            const rememberUser = this.cookie.get(
              'rememberUser' + user.UserId.toString()
            )
            if (rememberUser === 'true') {
              this.authenticationService.setClientSideSession(user.Script)
              this.store.dispatch(
                new SetServerSideSession(user, false, null, true)
              )
            } else {
              // this.store.dispatch(
              //   new SendTwoFactorAuthenticationNotification(user.UserId, false)
              // )
            }
          } else {
            this.authenticationService.setClientSideSession(user.Script)
            if (user.UserId == 1 && !(user.EmailAddress || '').trim()) {
              this.store.dispatch(
                new SetServerSideSession(
                  user,
                  false,
                  '/admin/system/user/edit?userID=1',
                  true
                )
              )
            } else {
              this.store.dispatch(
                new SetServerSideSession(user, false, null, true)
              )
            }
          }
          this.setupAppProjectValue(user.UserId)

          this.xsStore.dispatch(new GetUserLayoutId(user.UserId))
        }
        localStorage.setItem('loginTokenId', user.LoginTokenId)
        localStorage.setItem(
          'activeLastSessionChecked',
          moment(user.ActiveSessionLastChecked).format('YYYY-MM-DD h:mm:ss')
        )
      })

    this.store
      .pipe(
        select(getErrorMessage),
        takeUntil(this.unsubscribed$),
        map((error: string) => error),
        filter((error: string) => !!error)
      )
      .subscribe((error: string) => {
        setTimeout(() => {
          this.errorMessage = error
          this.showError = true
          this.isResetSuccessful = false
        })
      })

    this.store
      .pipe(
        select(isResetPasswordLinkSent),
        takeUntil(this.unsubscribed$),
        map((res: boolean) => res)
      )
      .subscribe((isLinkSent: boolean) => {
        if (isLinkSent) {
          this.isResetSuccessful = isLinkSent
          this.showError = false
        } else {
          this.isResetFailed = !isLinkSent
        }
        this.store.dispatch(new ClearStoreProperty('isResetPasswordLinkSent'))
      })

    this.showSpinner$ = this.store.pipe(
      select(showLoginSpinner),
      takeUntil(this.unsubscribed$)
    )

    /**
     * Dispatches actions for this page to fetch initial data.
     */
    this.store.dispatch(new FetchMaintenanceSetting())

    /**
     * Selects system maintenance data from the store.
     */
    this.store
      .pipe(
        select(maintenanceSettingInfo),
        filter((res) => !!res),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((maintenance: MaintenanceInfoModel) => {
        this.maintenanceInfo = maintenance
        this.maintenanceSetting()
      })

    // Handle IDP response validation logic here
    const params = this.activatedRoute.snapshot.queryParams

    if (params['status'] === 'success') {
      this.authenticationService
        .fetchLoginResponse<any>(params['si'])
        .pipe(take(1)) // Ensure only one subscription
        .subscribe((response) => {
          this.store.dispatch(new FetchBaseSettings())
          this.store.dispatch(new ShowLoginSpinner())
          this.store.dispatch(new SetIsAddOnBoxChecked(false))
          const idpLogin: Login = {
            userName: response.data.UserName,
            password: ''
          }
          this.store.dispatch(new FetchAuthToken(idpLogin))
        })
    }
  }

  #selectVodVersion(): void {
    combineLatest([
      this.store.select(getControlSetting('VOD_VERSION')),
      this.store.select(getControlSetting('ENABLE_REVIEW_2'))
    ])
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribed$))
      .subscribe({
        next: ([version, isFbiReview]) => {
          this.VOD_VERSION = Number(version)
          this.isFBIEnabled = Boolean(isFbiReview)
          this.isControlSettingLoading = false
        },
        error: () => {
          // Default if error occurs
          this.VOD_VERSION = 1
          this.isControlSettingLoading = false
        }
      })
  }

  setLoginBackground() {
    if (
      this.configService.loginImageBackgroundPath.startsWith('http://') ||
      this.configService.loginImageBackgroundPath.startsWith('https://')
    ) {
      this.loginBackgroundImagePath =
        this.configService.loginImageBackgroundPath
    } else {
      this.loginBackgroundImagePath =
        this.configService.getWebBaseUrl() +
        '/' +
        this.configService.loginImageBackgroundPath
    }
  }

  private setupAppProjectValue(userId: number) {
    // TODO: initially we load startup data when app loads for the first time.
    // TODO: user Id should derived from somewhere very specific service.
    // TODO: probably should dispatched from the place where the User ID resolves or config section.
    this.xsStore.dispatch(new InitStartupAction(userId))

    /* All of the admin sub-modules requires to use project list thus dispatch here whereas selectors may get values.
     Probably  should dispatch from the top level parent where cases are created.
     */
    this.xsStore.dispatch([new FetchProjectList()])
  }

  ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
    this.store.dispatch(new ClearStoreProperty('error'))
  }

  onUserNameChange($event) {
    this.login = {
      ...this.login,
      userName: $event.target.value
    }
  }

  onPasswordChange($event) {
    this.login = {
      ...this.login,
      password: $event.target.value
    }
  }

  toggleForgetPassword() {
    this.forgotPassword = !this.forgotPassword
    this.showError = false
    this.store.dispatch(new ClearStoreProperty('error'))
  }

  onLogin(): void {
    if (this.loginForm.valid) {
      this.store.dispatch(new FetchBaseSettings())
      this.store.dispatch(new ShowLoginSpinner())
      this.store.dispatch(new SetIsAddOnBoxChecked(false))
      if (this.forgotPassword) {
        this.store.dispatch(new ClearStoreProperty('error'))
        this.store.dispatch(
          new SendResetPasswordLink(this.login.userNameForgotten)
        )
      } else {
        this.store.dispatch(new FetchAuthToken(this.login))
      }
    }
  }

  onCloseLoginFailed(): void {
    this.showError = false
    this.store.dispatch(new ClearStoreProperty('error'))
  }

  onClosePasswordSent(): void {
    this.isResetSuccessful = false
    this.isResetFailed = false
    this.showError = false
    this.store.dispatch(new ClearStoreProperty('error'))
  }

  onGoToLogin(): void {
    this.forgotPassword = !this.forgotPassword
    this.showError = false
    this.store.dispatch(new ClearStoreProperty('error'))
  }

  isBrowserVersionSupported() {
    const res = this.getBrowserInfo('All')
    this.notSupported = false
    const browserInfo = res.split(',')
    if (browserInfo[0].indexOf('IE') >= 0) {
      this.clearCachePath = '../App/Views/ClearCache/clear-cache-ie.html'
      localStorage.setItem('ClearCachePath', this.clearCachePath)
      this.notSupported = browserInfo[1] <= 11
    } else if (browserInfo[0].indexOf('Edge') >= 0) {
      this.clearCachePath = '../App/Views/ClearCache/clear-cache-edge.html'
      localStorage.setItem('ClearCachePath', this.clearCachePath)
      this.notSupported = browserInfo[1] < 18
    } else if (browserInfo[0].indexOf('Chrome') >= 0) {
      this.clearCachePath = '../App/Views/ClearCache/clear-cache-chrome.html'
      localStorage.setItem('ClearCachePath', this.clearCachePath)
      this.notSupported = browserInfo[1] < 51
    } else if (browserInfo[0].indexOf('Firefox') >= 0) {
      this.clearCachePath = '../App/Views/ClearCache/clear-cache-firefox.html'
      localStorage.setItem('ClearCachePath', this.clearCachePath)
      this.notSupported = browserInfo[1] < 47
    } else if (browserInfo[0].indexOf('Safari') >= 0) {
      this.clearCachePath = '../App/Views/ClearCache/clear-cache-safari.html'
      localStorage.setItem('ClearCachePath', this.clearCachePath)
      this.notSupported = browserInfo[1] < 9
    }
    if (this.notSupported) {
      this.browserSupportMsg =
        'Latest version of ' + browserInfo[0] + 'browser is recommended'
      this.isBrowserVersionSupportedFlag = false
    } else {
      this.browserSupportMsg = ' Browser Version is  supported'
      this.isBrowserVersionSupportedFlag = true
    }
  }

  getBrowserInfo(getInfoFor): any {
    const ua = navigator.userAgent
    let tem
    let M =
      ua.match(
        /(opera|chrome|safari|firefox|msie|edge|trident(?=\/))\/?\s*(\d+)/i
      ) || []
    if (/trident/i.test(M[1])) {
      tem = /\brv[ :]+(\d+)/g.exec(ua) || []
      if (getInfoFor === 'Browser') {
        return 'IE'
      } else {
        return 'IE' + ',' + tem[1]
      }
    }
    if (M[1] === 'Chrome') {
      tem = ua.match(/\bOPR\/(\d+)/)
      if (tem != null) {
        return 'Opera'
      }
    }
    // add Edge
    if (M[1] === 'Chrome') {
      tem = ua.match(/\Edge\/(\d+)/)
      if (tem != null) {
        if (getInfoFor === 'Browser') {
          return 'Edge'
        } else {
          return 'Edge' + ',' + tem[1]
        }
      }
    }
    M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?']
    if ((tem = ua.match(/version\/(\d+)/i)) != null) {
      M.splice(1, 1, tem[1])
    }
    if (getInfoFor === 'Browser') {
      return M[0]
    } else {
      return M[0] + ',' + M[1]
    }
  }

  areCookiesEnabled(): boolean {
    this.isCookieEnabled = navigator.cookieEnabled

    if (
      typeof navigator.cookieEnabled === 'undefined' &&
      !this.isCookieEnabled
    ) {
      document.cookie = 'testcookie'
      this.isCookieEnabled = document.cookie.indexOf('testcookie') !== -1
    }
    return this.isCookieEnabled
  }

  /**
   *check if system maintenance is enable or not and check the maintenance prior start date
     and maintenance start date
   */
  maintenanceSetting() {
    if (this.maintenanceInfo.isEnable) {
      const currentDate = new Date()
      const savedDate = new Date(this.maintenanceInfo.startedOn)
      const startDate = new Date(this.maintenanceInfo.startedOn)
      startDate.setDate(
        startDate.getDate() - this.maintenanceInfo.priorAlertDays
      )

      if (currentDate >= savedDate) {
        this.isMaintenanceEnable = true
      } else if (startDate <= currentDate && currentDate < savedDate) {
        this.showAlertMsg = true
      }
    }
  }

  #notifyToLoadChangeCurrentPassword(remarkMessage: string): void {
    this.iframeMessengerService.sendMessage({
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'FRAME_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VOD,
      payload: {
        type: MessageType.NOTIFY_CHANGE,
        content: {
          openChangeCurrentPassword: true,
          username: this.login.userName,
          password: this.login.password,
          remarkMessage
        }
      }
    })
  }

  private readonly handlePwChange = (
    passwordExpired: boolean,
    remark: string
  ): void => {
    if (!this.isFBIEnabled) {
      this.#notifyToLoadChangeCurrentPassword(remark)
    } else {
      const ref = this.dialog.open(ChangePasswordComponent, {
        disableClose: true,
        closeOnNavigation: true,
        width: '30vw',
        data: passwordExpired
      })

      ref
        .afterClosed()
        .pipe(takeUntil(this.unsubscribed$))
        .subscribe(() => {
          localStorage.clear()
          this.store.dispatch(new GlobalLogOutAction())
          this._router.navigateByUrl('/login')
        })
    }
  }

  #selectLoginResponseFromChildApp(): void {
    // Step 1: Listen to iframe messages
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (m) =>
            m.eventTriggeredBy === AppIdentitiesTypes.VENIO_NEXT &&
            m.eventTriggeredFor === 'PARENT_WINDOW' &&
            ((m.payload as MessageContent).content['isLoginSuccess'] ||
              (m.payload as MessageContent)?.type ===
                MessageType.IDP_AUTHENTICATE)
        ),
        map((mc) => mc.payload as MessageContent),
        debounceTime(100),

        // Step 2: Handle the message and dispatch actions
        concatMap((mc: MessageContent) => {
          if (mc.content['isLoginSuccess']) {
            const { tokenData } = mc.content as any

            // Dispatch actions and store tokens in local storage
            this.store.dispatch(new FetchBaseSettings())

            Object.keys(tokenData)
              .filter(
                (key) => tokenData[key] !== undefined && tokenData[key] !== null
              )
              .forEach((key) => {
                localStorage.setItem(key, tokenData[key])
              })
          }
          // Proceed to the next step after dispatching actions
          return of(mc)
        }),

        // Step 3: Wait for the user to be loaded
        concatMap((mc: any) => {
          if (mc.content['isLoginSuccess']) {
            const { tokenData, formData } = mc.content as any

            const decryptedPassword = this.authenticationService.decryptStr(
              formData.password,
              tokenData['access_token']
            )
            this.store.dispatch(new FetchUserDetails(decryptedPassword))
            return this.store.pipe(
              select(getUser),
              filter((user) => !!user),
              map((user) => ({ mc, user }))
            )
          }
          return of({ mc, user: null })
        }),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(({ mc, user }) => {
        if (mc.content['isLoginSuccess']) {
          // Step 4: After the user is loaded, perform the remaining operations

          // Hide the login spinner
          this.store.dispatch(new HideLoginSpinner())

          const { tokenData, formData, rememberMeTimeSpan } = mc.content as any

          if (formData.rememberMe) {
            this.cookie.set('rememberMe', 'true', rememberMeTimeSpan)
          }

          // Decrypt the password if necessary
          const decryptedPassword = this.authenticationService.decryptStr(
            formData.password,
            tokenData['access_token']
          )

          this.login = {
            userName: formData.username,
            password: decryptedPassword
          }

          // Dispatch additional actions
          this.store.dispatch(new SetIsAddOnBoxChecked(false))
          this.store.dispatch(new FetchAuthTokenSuccess(tokenData))

          // Set client-side and server-side sessions
          this.authenticationService.setClientSideSession(user?.Script)
          this.store.dispatch(
            new SetServerSideSession(user, false, undefined, true)
          )
        } else if (mc.type === MessageType.IDP_AUTHENTICATE) {
          window.location.href = mc.content['idpLoginUrl']
        }
      })
  }
}
