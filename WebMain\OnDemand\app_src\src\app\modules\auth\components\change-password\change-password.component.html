<!-- <div class="row" style="padding:15px 25px">
  <img src="{{configService.getWebBaseUrl() + '/' + configService.logoPath}}" width="90" height="62"
      alt="">
</div>  -->
<div [ngStyle]="{ display: !isFBIEnabled ? 'none' : 'block' }">
  <form [formGroup]="changePasswordForm">
    <div class="row mb-3 mt-3">
      <div class="col-8">
        <div class="block-title-text">Change Password</div>
      </div>
      <div class="col-4 text-right modal-header">
        <button
          type="button"
          class="close"
          *ngIf="passWordExpired"
          mat-dialog-close
        >
          <span class="fa fa-times"></span>
        </button>
      </div>
      <div class="col-md-12">
        <div
          class="alert alert-danger alert-dismissible"
          *ngIf="complexityOptions?.length > 0"
        >
          <a
            href="javascript:;"
            class="close"
            data-dismiss="alert"
            aria-label="close"
            title="close"
            >×</a
          >
          Password Criteria:
          <ul>
            <li *ngFor="let o of complexityOptions">{{ o }}</li>
          </ul>
        </div>
      </div>
    </div>
    <div class="row form-group" *ngIf="!isForcedReset">
      <div [ngClass]="{ 'col-3': !passWordExpired, 'col-4': passWordExpired }">
        Current Password
      </div>
      <div [ngClass]="{ 'col-7': !passWordExpired, 'col-8': passWordExpired }">
        <input
          id="oldPassword"
          type="password"
          Class="form-control"
          placeholder="Current Password"
          formControlName="oldPassword"
          [ngClass]="{
            'is-invalid': changePasswordSubmitted && form.oldPassword.errors
          }"
        />
        <div
          *ngIf="changePasswordSubmitted && form.oldPassword.errors"
          class="text-danger"
        >
          <div *ngIf="form.oldPassword.errors?.required">
            <small>Current password is required</small>
          </div>
        </div>
      </div>
    </div>
    <div class="row form-group">
      <div [ngClass]="{ 'col-3': !passWordExpired, 'col-4': passWordExpired }">
        New Password
      </div>
      <div [ngClass]="{ 'col-7': !passWordExpired, 'col-8': passWordExpired }">
        <input
          id="newPassword"
          type="password"
          Class="form-control custome-input"
          placeholder="New Password"
          formControlName="newPassword"
          autocomplete="new-password"
          [ngClass]="{
            'is-invalid': changePasswordSubmitted && form.newPassword.errors
          }"
        />
        <div
          *ngIf="changePasswordSubmitted && form.newPassword.errors"
          class="text-danger"
        >
          <div *ngIf="form.newPassword.errors?.required">
            <small>new password is required</small>
          </div>
        </div>
      </div>
    </div>
    <div class="row form-group">
      <div [ngClass]="{ 'col-3': !passWordExpired, 'col-4': passWordExpired }">
        Confirm New Password
      </div>
      <div [ngClass]="{ 'col-7': !passWordExpired, 'col-8': passWordExpired }">
        <input
          id="confirmPassword"
          type="password"
          Class="form-control custome-input"
          placeholder="Confirm Password"
          formControlName="confirmPassword"
          autocomplete="new-password"
          [ngClass]="{
            'is-invalid': changePasswordSubmitted && form.confirmPassword.errors
          }"
        />
        <div
          *ngIf="changePasswordSubmitted && form.confirmPassword.errors"
          class="text-danger"
        >
          <div *ngIf="form.confirmPassword.errors?.required">
            <small>Confirm password is required</small>
          </div>
          <div *ngIf="form.confirmPassword.errors?.notEquivalent">
            <small> Password and Confirm Password did not match.</small>
          </div>
        </div>
      </div>
    </div>
    <div class="row form-group">
      <div
        [ngClass]="{ 'col-3': !passWordExpired, 'col-4': passWordExpired }"
      ></div>
      <div
        [ngClass]="{ 'col-7': !passWordExpired, 'col-8': passWordExpired }"
        class="text-right"
      >
        <button
          class="btn btn-{{ client }}-primary"
          type="submit"
          (click)="onChangePassword()"
          [disabled]="disableButton"
        >
          Change Password
        </button>
        <button
          type="button"
          class="btn btn-grey ml-2"
          *ngIf="passWordExpired"
          mat-dialog-close
        >
          Cancel
        </button>
      </div>
    </div>
  </form>
</div>
<div *ngIf="!isFBIEnabled" class="iframe-container">
  <div
    *ngIf="passWordExpired"
    class="w-100 text-right modal-header"
    style="z-index: 2; margin-top: 10px"
  >
    <button type="button" class="close" mat-dialog-close>
      <span class="fa fa-times"></span>
    </button>
  </div>
  <app-micro-app-auth-loader
    [customPath]="iframeCustomPath"
    [iframeIdentity]="appIdentitiesTypes.VENIO_AUTH">
  </app-micro-app-auth-loader>
</div>
