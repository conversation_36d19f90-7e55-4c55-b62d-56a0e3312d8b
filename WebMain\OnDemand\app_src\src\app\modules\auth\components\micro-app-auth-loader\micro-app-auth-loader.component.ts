import {
  AfterViewInit,
  Component,
  Element<PERSON>ef,
  InjectionToken,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild
} from '@angular/core'
import { <PERSON>Sanitizer, SafeResourceUrl } from '@angular/platform-browser'
import { ActivatedRoute } from '@angular/router'
import { MicroAppAuthService } from '@auth/services/micro-app-auth.service'
import { AuthState } from '@auth/store/reducers'
import { FetchControlSettings } from '@config/store/actions'
import { Store } from '@ngrx/store'
import { IframeMessengerService } from '@root/modules/micro-apps/config/iframe-messenger.service'
import { AppIdentitiesTypes } from '@root/modules/micro-apps/models/app-identities-types'
import { environment } from '../../../../../environments/environment'

export const CUSTOM_PATH = new InjectionToken<string>('CUSTOM_PATH')

@Component({
  selector: 'app-micro-app-auth-loader',
  templateUrl: './micro-app-auth-loader.component.html',
  styleUrls: ['./micro-app-auth-loader.component.scss'],
  providers: [MicroAppAuthService]
})
export class MicroAppAuthLoaderComponent implements AfterViewInit, OnDestroy {
  public readonly appIdentitiesTypes = AppIdentitiesTypes

  private iframeCustomPath: string

  @ViewChild('iframeEl', { static: true })
  private readonly iframeElement: ElementRef<HTMLIFrameElement>

  get #iframe(): HTMLIFrameElement {
    return this.iframeElement?.nativeElement
  }

  venioNextUrl: SafeResourceUrl

  /**
   * The specified version of the VOD version in control settings.
   */
  VOD_VERSION: number

  get vodVersion(): number {
    return this.VOD_VERSION
  }

  /**
   * Constructs the iframe URL for the micro app, accommodating both development and production environments.
   * This method uses the current window's hash fragment and merges any existing query parameters with additional parameters provided.
   *
   * @param {Record<string, any>} additionalParams - Additional query parameters to append to the URL.
   * @returns {string} The constructed iframe URL.
   */
  private getUrl(additionalParams: Record<string, any> = {}): string {
    // The micro app port for development is 4300.
    // If you change the port of the target micro app, please update it here.
    const localDevelopmentPort = ':4300'

    // Determine the path segment or port based on the environment.
    const pathSegmentOrPort = !environment.production
      ? localDevelopmentPort
      : ''

    // Base URL for the micro app.
    // In production, it becomes something like /VenioWeb/OnDemand/venio-next
    const microAppBaseUrl =
      `${environment.microAppDeployUrl}${pathSegmentOrPort}`.replace(
        /([^:]\/)\/+/g,
        '$1'
      )

    // Get the current window location's hash fragment,
    // which includes the path and query parameters after the '#' symbol.
    const currentHash = window.location.hash // e.g., #/login?ReturnUrl=...&session=expire

    // Separate the hash fragment into path and query string.
    const [hashPath, hashQueryString] = currentHash.split('?')

    // Parse the existing query parameters from the hash.
    const existingParams = new URLSearchParams(hashQueryString || '')

    // Merge additional parameters, overwriting existing ones if necessary.
    for (const [key, value] of Object.entries(additionalParams)) {
      if (value !== undefined && value !== null) {
        existingParams.set(key, String(value))
      }
    }

    // Build the new query string.
    const newQueryString = existingParams.toString()

    // Reconstruct the final hash fragment.
    const newHash = newQueryString
      ? `${this.iframeCustomPath || hashPath}?${newQueryString}`
      : this.iframeCustomPath || hashPath

    // Combine to form the final URL for the iframe.
    return `${microAppBaseUrl}/${newHash}`
  }

  constructor(
    public microAppAuthService: MicroAppAuthService,
    public store: Store<AuthState>,
    public iframeMessengerService: IframeMessengerService,
    public sanitizer: DomSanitizer,
    public activatedRoute: ActivatedRoute
  ) {
    this.venioNextUrl = sanitizer.bypassSecurityTrustResourceUrl('')
    // As soon as the component is created, we dispatch the action to fetch the control settings
    this.store.dispatch(new FetchControlSettings())
  }

  public ngAfterViewInit(): void {
    this.iframeCustomPath = this.microAppAuthService.getPath
    this.setIframeSrc()
  }

  public ngOnDestroy(): void {
    this.microAppAuthService.clearPath()
  }

  setIframeSrc(): void {
    if (!this.#iframe) return

    // Extract query parameters from the current activatedRoute
    const queryParams = this.activatedRoute.snapshot.queryParams

    const url = this.getUrl(queryParams)

    /**
     * Sanitize url properly & securely
     * @see https://stackoverflow.com/a/69211616/4444844
     */
    this.venioNextUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url)
  }
}
